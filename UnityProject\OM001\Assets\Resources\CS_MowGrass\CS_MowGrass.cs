using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Rendering;
using Debug = UnityEngine.Debug;
using Random = UnityEngine.Random;
using Unity.Burst;
using Unity.Jobs;
using Unity.Collections;

namespace GameWish.Game
{
    public class CS_MowGrass : MonoBehaviour
    {
        [SerializeField] private Mesh m_Grass;
        [SerializeField] private Mesh m_GrassSourceMesh;
        [SerializeField] private Shader m_GrassShader;
        [SerializeField] private Material m_GrassMaterial;

        [SerializeField] private Color m_TopColor = Color.white;
        [SerializeField] private Color m_BottomColor = Color.white;

        [SerializeField] private Vector2 m_HeightRandom = new Vector2(0.8f, 1.4f);
        [SerializeField] private Vector2 m_WidthRandom = new Vector2(0.8f, 1.4f);
        [SerializeField] private float m_RotateRandom = 45f;

        [SerializeField] private Color m_WaveSinColor = Color.white;
        [SerializeField] private float m_WaveA = 0.2f;
        [SerializeField] private float m_WaveS = 1f;
        [SerializeField] private float m_WaveL = 10f;

        private Material m_Material;

        NativeArray<float> sizes;
        NativeArray<float> cumulativeSizes;
        NativeArray<float> total;
        public GameObject[] ObjectsArray;
        public int cycles = 1;

        private struct SourceVertex
        {
            public Matrix4x4 mat;
            public Vector2 uv;
            public Vector2 sourceUV;

            public static int GetSize()
            {
                return sizeof(float) * (16 + 2 + 2);
            }
        };

        private bool m_Initialized;
        private ComputeBuffer m_DrawBuffer;
        private ComputeBuffer m_ArgsBuffer;

        private Bounds m_LocalBound;
        private uint[] m_Args = new uint[] { 0, 1, 0, 0, 0 };

        private void LateUpdate()
        {
            if (m_Initialized)
            {
                StartDraw();
            }
        }

        private void OnDestroy()
        {
            Release();
        }

        public void Generate()
        {
            GenerateFromArray(ObjectsArray);
        }

        public void GenerateFromArray(GameObject[] arrayToUse)
        {
            for (int i = 0; i < cycles; i++)
            {
                for (int j = 0; j < arrayToUse.Length; j++)
                {
                    GeneratePositions(arrayToUse[j]);
                }
            }
        }

        public void GeneratePositions(GameObject selection)
        {

            // mesh
            if (selection.TryGetComponent(out MeshFilter sourceMesh))
            {

                CalcAreas(sourceMesh.sharedMesh);
                Matrix4x4 localToWorld = sourceMesh.transform.localToWorldMatrix;

                var oTriangles = sourceMesh.sharedMesh.triangles;
                var oVertices = sourceMesh.sharedMesh.vertices;
                var oColors = sourceMesh.sharedMesh.colors;
                var oNormals = sourceMesh.sharedMesh.normals;

                var meshTriangles = new NativeArray<int>(oTriangles.Length, Allocator.Temp);
                var meshVertices = new NativeArray<Vector4>(oVertices.Length, Allocator.Temp);
                var meshColors = new NativeArray<Color>(oVertices.Length, Allocator.Temp);
                var meshNormals = new NativeArray<Vector3>(oNormals.Length, Allocator.Temp);
                for (int i = 0; i < meshTriangles.Length; i++)
                {
                    meshTriangles[i] = oTriangles[i];
                }

                for (int i = 0; i < meshVertices.Length; i++)
                {
                    meshVertices[i] = oVertices[i];
                    meshNormals[i] = oNormals[i];
                    if (oColors.Length == 0)
                    {
                        meshColors[i] = Color.black;
                    }
                    else
                    {
                        meshColors[i] = oColors[i];
                    }

                }

                var point = new NativeArray<Vector3>(1, Allocator.Temp);

                var normals = new NativeArray<Vector3>(1, Allocator.Temp);

                var lengthWidth = new NativeArray<float>(1, Allocator.Temp);
                var job = new MyJob
                {
                    CumulativeSizes = cumulativeSizes,
                    MeshColors = meshColors,
                    MeshTriangles = meshTriangles,
                    MeshVertices = meshVertices,
                    MeshNormals = meshNormals,
                    Total = total,
                    Sizes = sizes,
                    Point = point,
                    Normals = normals,
                    VertexColorSettings = toolSettings.VertexColorSettings,
                    VertexFade = toolSettings.VertexFade,
                    LengthWidth = lengthWidth,
                };


                Bounds bounds = sourceMesh.sharedMesh.bounds;

                Vector3 meshSize = new Vector3(
                    bounds.size.x * sourceMesh.transform.lossyScale.x,
                    bounds.size.y * sourceMesh.transform.lossyScale.y,
                    bounds.size.z * sourceMesh.transform.lossyScale.z
                );
                meshSize += Vector3.one;

                float meshVolume = meshSize.x * meshSize.y * meshSize.z;
                int numPoints = Mathf.Min(Mathf.FloorToInt(meshVolume * toolSettings.generationDensity), toolSettings.grassAmountToGenerate);


                for (int j = 0; j < numPoints; j++)
                {
                    job.Execute();
                    GrassData newData = new();
                    Vector3 newPoint = point[0];
                    newData.position = localToWorld.MultiplyPoint3x4(newPoint);

                    Collider[] cols = Physics.OverlapBox(newData.position, Vector3.one * 0.2f, Quaternion.identity, toolSettings.paintBlockMask);
                    if (cols.Length > 0)
                    {
                        newPoint = Vector3.zero;
                    }
                    // check normal limit

                    Vector3 worldNormal = selection.transform.TransformDirection(normals[0]);

                    if (worldNormal.y <= (1 + toolSettings.normalLimit) && worldNormal.y >= (1 - toolSettings.normalLimit))
                    {

                        if (newPoint != Vector3.zero)
                        {
                            newData.color = GetRandomColor();
                            newData.length = new Vector2(toolSettings.sizeWidth, toolSettings.sizeLength) * lengthWidth[0];
                            newData.normal = worldNormal;
                            grassComputeScript.SetGrassPaintedDataList.Add(newData);
                        }
                    }
                }

                sizes.Dispose();
                cumulativeSizes.Dispose();
                total.Dispose();
                meshColors.Dispose();
                meshTriangles.Dispose();
                meshVertices.Dispose();
                meshNormals.Dispose();
                point.Dispose();
                lengthWidth.Dispose();

                RebuildMesh();
            }

            else if (selection.TryGetComponent(out Terrain terrain))
            {
                // terrainmesh



                float meshVolume = terrain.terrainData.size.x * terrain.terrainData.size.y * terrain.terrainData.size.z;
                int numPoints = Mathf.Min(Mathf.FloorToInt(meshVolume * toolSettings.generationDensity), toolSettings.grassAmountToGenerate);


                for (int j = 0; j < numPoints; j++)
                {
                    Matrix4x4 localToWorld = terrain.transform.localToWorldMatrix;
                    GrassData newData = new();
                    Vector3 newPoint = Vector3.zero;
                    Vector3 newNormal = Vector3.zero;
                    float[,,] maps = new float[0, 0, 0];
                    GetRandomPointOnTerrain(localToWorld, ref maps, terrain, terrain.terrainData.size, ref newPoint, ref newNormal);
                    newData.position = newPoint;

                    Collider[] cols = Physics.OverlapBox(newData.position, Vector3.one * 0.2f, Quaternion.identity, toolSettings.paintBlockMask);
                    if (cols.Length > 0)
                    {
                        newPoint = Vector3.zero;
                    }


                    float getFadeMap = 0;
                    // check map layers
                    for (int i = 0; i < maps.Length; i++)
                    {
                        getFadeMap += System.Convert.ToInt32(toolSettings.layerFading[i]) * maps[0, 0, i];
                        if (maps[0, 0, i] > toolSettings.layerBlocking[i])
                        {
                            newPoint = Vector3.zero;
                        }
                    }

                    if (newNormal.y <= (1 + toolSettings.normalLimit) && newNormal.y >= (1 - toolSettings.normalLimit))
                    {
                        float fade = Mathf.Clamp((getFadeMap), 0, 1f);
                        newData.color = GetRandomColor();
                        newData.length = new Vector2(toolSettings.sizeWidth, toolSettings.sizeLength * fade);
                        newData.normal = newNormal;
                        if (newPoint != Vector3.zero)
                        {
                            grassComputeScript.SetGrassPaintedDataList.Add(newData);
                        }
                    }


                }
                RebuildMesh();
            }

        }

        public void CalcAreas(Mesh mesh)
        {
            sizes = GetTriSizes(mesh.triangles, mesh.vertices);
            cumulativeSizes = new NativeArray<float>(sizes.Length, Allocator.Temp);
            total = new NativeArray<float>(1, Allocator.Temp);

            for (int i = 0; i < sizes.Length; i++)
            {
                total[0] += sizes[i];
                cumulativeSizes[i] = total[0];
            }
        }

        [Button("Init")]
        public void Initialize()
        {
            if (m_Initialized)
            {
                Release();
            }

            m_Material = new Material(m_GrassShader);
            m_Material.CopyPropertiesFromMaterial(m_GrassMaterial);

            Vector3[] positions = m_GrassSourceMesh.vertices;
            Vector2[] uvs = m_GrassSourceMesh.uv;

            List<SourceVertex> vertices = new List<SourceVertex>();
            for (int i = 0; i < positions.Length; i++)
            {
                Vector4 pos = transform.localToWorldMatrix * new Vector4(positions[i].x, positions[i].y, positions[i].z, 1);
                Vector3 scale = new Vector3(Random.Range(m_WidthRandom.x, m_WidthRandom.y), Random.Range(m_HeightRandom.x, m_HeightRandom.y), 1);
                Quaternion rot = Quaternion.Euler(0, Random.Range(-m_RotateRandom, m_RotateRandom), 0);

                vertices.Add(new SourceVertex()
                {
                    mat = Matrix4x4.TRS(pos, rot, scale),
                    uv = new Vector2(0, 0),
                    sourceUV = uvs[i]
                });
            }

            m_DrawBuffer = new ComputeBuffer(positions.Length, SourceVertex.GetSize());
            m_DrawBuffer.SetData(vertices);

            m_Args[0] = (uint)m_Grass.GetIndexCount(0);
            m_Args[1] = (uint)positions.Length;
            m_Args[2] = (uint)m_Grass.GetIndexStart(0);
            m_Args[3] = (uint)m_Grass.GetBaseVertex(0);
            m_Args[4] = (uint)0;

            m_ArgsBuffer = new ComputeBuffer(1, sizeof(uint) * m_Args.Length, ComputeBufferType.IndirectArguments);
            m_ArgsBuffer.SetData(m_Args);

            m_Material.SetBuffer("_DrawTriangles", m_DrawBuffer);

            m_LocalBound = m_GrassSourceMesh.bounds;
            m_LocalBound.center = transform.position;

            m_Initialized = true;
        }

        public void SetMainTexture(Texture2D value)
        {
            if (m_Material != null)
            {
                m_Material.SetTexture("_MainTex", value);
            }
        }

        private void Release()
        {
            if (m_Initialized)
            {
                m_DrawBuffer.Release();
                m_ArgsBuffer.Release();
                DestroyImmediate(m_Material);
            }

            m_Initialized = false;
        }

        private void StartDraw()
        {
            m_ArgsBuffer.SetData(m_Args);

            m_Material.SetColor("_TopColor", m_TopColor);
            m_Material.SetColor("_BottomColor", m_BottomColor);

            m_Material.SetColor("_WaveSinColor", m_WaveSinColor);
            m_Material.SetFloat("_WaveA", m_WaveA);
            m_Material.SetFloat("_WaveL", m_WaveL);
            m_Material.SetFloat("_WaveS", m_WaveS);

            Graphics.DrawMeshInstancedIndirect(m_Grass, 0, m_Material, m_LocalBound, m_ArgsBuffer);
        }
    }
}